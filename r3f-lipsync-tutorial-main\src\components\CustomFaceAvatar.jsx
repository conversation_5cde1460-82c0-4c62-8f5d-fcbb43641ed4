import React, { useEffect, useRef, forwardRef, useImperativeHandle } from 'react';
import { useFrame } from '@react-three/fiber';
import * as THREE from 'three';

/**
 * CustomFaceAvatar component that integrates custom 3D faces with lip-sync animation
 * This component can replace the default Avatar when a custom face is available
 */
const CustomFaceAvatar = forwardRef(({ 
  customFaceMesh, 
  speak, 
  text, 
  facialExpressions,
  ...props 
}, ref) => {
  const groupRef = useRef();
  const faceRef = useRef();
  const currentVisemeRef = useRef('');
  const visemeWeightRef = useRef(0);

  // Expose methods to parent components
  useImperativeHandle(ref, () => ({
    speak: (audioUrl, text) => {
      // This would integrate with the existing speech system
      console.log('Custom face avatar speaking:', text);
    },
    setViseme: (viseme, weight = 1.0) => {
      if (faceRef.current && faceRef.current.setViseme) {
        faceRef.current.setViseme(viseme, weight);
      }
    },
    resetVisemes: () => {
      if (faceRef.current && faceRef.current.resetVisemes) {
        faceRef.current.resetVisemes();
      }
    }
  }));

  // Set up the custom face mesh
  useEffect(() => {
    if (customFaceMesh && groupRef.current) {
      // Clear existing children
      while (groupRef.current.children.length > 0) {
        groupRef.current.remove(groupRef.current.children[0]);
      }

      // Clone the custom face mesh to avoid modifying the original
      const faceMeshClone = customFaceMesh.clone();
      faceMeshClone.position.set(0, 0, 0);
      faceMeshClone.scale.set(1, 1, 1);
      
      // Store reference for animation
      faceRef.current = faceMeshClone;
      
      groupRef.current.add(faceMeshClone);
      
      console.log('Custom face mesh added to avatar');
    }
  }, [customFaceMesh]);

  // Handle facial expressions and visemes
  useEffect(() => {
    if (facialExpressions && faceRef.current) {
      // Apply facial expressions if available
      Object.entries(facialExpressions).forEach(([expression, weight]) => {
        if (faceRef.current.setViseme) {
          faceRef.current.setViseme(expression, weight);
        }
      });
    }
  }, [facialExpressions]);

  // Animation loop for smooth transitions
  useFrame((state, delta) => {
    if (faceRef.current && faceRef.current.morphTargetInfluences) {
      // Smooth viseme transitions
      const targetWeight = speak ? 0.8 : 0;
      visemeWeightRef.current = THREE.MathUtils.lerp(
        visemeWeightRef.current, 
        targetWeight, 
        delta * 5
      );

      // Apply current viseme with smooth weight
      if (currentVisemeRef.current && faceRef.current.morphTargetDictionary) {
        const index = faceRef.current.morphTargetDictionary[currentVisemeRef.current];
        if (index !== undefined) {
          faceRef.current.morphTargetInfluences[index] = visemeWeightRef.current;
        }
      }
    }

    // Add subtle breathing animation
    if (groupRef.current) {
      const breathingScale = 1 + Math.sin(state.clock.elapsedTime * 2) * 0.02;
      groupRef.current.scale.setScalar(breathingScale);
    }
  });

  return (
    <group ref={groupRef} {...props}>
      {/* Placeholder for when no custom face is available */}
      {!customFaceMesh && (
        <mesh>
          <sphereGeometry args={[0.5, 32, 32]} />
          <meshStandardMaterial color="#ffdbac" />
        </mesh>
      )}
    </group>
  );
});

CustomFaceAvatar.displayName = 'CustomFaceAvatar';

export default CustomFaceAvatar;
