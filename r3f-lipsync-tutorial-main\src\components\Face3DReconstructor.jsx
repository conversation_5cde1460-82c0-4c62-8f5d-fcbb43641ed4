import React, { useState, useRef, useCallback, useEffect } from 'react';
import { Canvas } from '@react-three/fiber';
import { OrbitControls, Environment } from '@react-three/drei';
import { face3DReconstructor } from '../services/face3DReconstructor';
import * as THREE from 'three';
import { GLTFExporter } from 'three-stdlib';

/**
 * 3D Face Reconstruction Component
 * Allows users to upload an image and generate a 3D face model
 */
const Face3DReconstructor = ({ onFaceGenerated }) => {
  const [isProcessing, setIsProcessing] = useState(false);
  const [reconstructedFace, setReconstructedFace] = useState(null);
  const [error, setError] = useState(null);
  const [progress, setProgress] = useState('');
  const fileInputRef = useRef(null);

  const handleImageUpload = useCallback(async (event) => {
    const file = event.target.files[0];
    if (!file) return;

    setIsProcessing(true);
    setError(null);
    setProgress('Loading image...');

    try {
      // Create image element
      const img = new Image();
      img.crossOrigin = 'anonymous';

      await new Promise((resolve, reject) => {
        img.onload = resolve;
        img.onerror = reject;
        img.src = URL.createObjectURL(file);
      });

      setProgress('Initializing 3D reconstruction...');

      // Initialize the reconstructor if needed
      await face3DReconstructor.initialize();

      setProgress('Detecting face landmarks...');

      // Reconstruct 3D face
      const result = await face3DReconstructor.reconstruct3DFace(img);

      setProgress('Creating 3D animated mesh...');

      // Create the animated 3D mesh with morphTargets
      const faceMesh = face3DReconstructor.createAnimatedFaceMesh(result);

      setReconstructedFace({
        mesh: faceMesh,
        landmarks: result.landmarks,
        blendshapes: result.blendshapes,
        originalImage: img.src,
        availableVisemes: faceMesh.getAvailableVisemes()
      });

      setProgress('3D face reconstruction complete!');

      // Notify parent component
      if (onFaceGenerated) {
        onFaceGenerated(faceMesh, result);
      }

      // Clean up
      URL.revokeObjectURL(img.src);

    } catch (err) {
      console.error('Face reconstruction error:', err);
      setError(err.message || 'Failed to reconstruct 3D face');
    } finally {
      setIsProcessing(false);
      setTimeout(() => setProgress(''), 3000);
    }
  }, [onFaceGenerated]);

  const ReconstructedFaceMesh = ({ faceMesh }) => {
    const meshRef = useRef();
    const [currentViseme, setCurrentViseme] = useState('');

    useEffect(() => {
      if (faceMesh) {
        // Demo animation - cycle through visemes
        const visemes = ['viseme_PP', 'viseme_AA', 'viseme_O', 'viseme_I'];
        let currentIndex = 0;

        const animateVisemes = () => {
          if (faceMesh && faceMesh.morphTargetInfluences) {
            // Reset all visemes
            faceMesh.morphTargetInfluences.fill(0);

            // Set current viseme
            const viseme = visemes[currentIndex];
            if (faceMesh.morphTargetDictionary && faceMesh.morphTargetDictionary[viseme] !== undefined) {
              const index = faceMesh.morphTargetDictionary[viseme];
              faceMesh.morphTargetInfluences[index] = 0.8;
              setCurrentViseme(viseme);
            }

            currentIndex = (currentIndex + 1) % visemes.length;
          }
        };

        // Start demo animation
        const interval = setInterval(animateVisemes, 1500);

        return () => clearInterval(interval);
      }
    }, [faceMesh]);

    if (!faceMesh) return null;

    return (
      <group>
        <primitive
          ref={meshRef}
          object={faceMesh}
          position={[0, 0, 0]}
          scale={[1.5, 1.5, 1.5]}
          rotation={[0, 0, 0]}
        />
        {/* Add a subtle animation indicator */}
        <mesh position={[0, -1.2, 0]}>
          <planeGeometry args={[0.8, 0.1]} />
          <meshBasicMaterial
            color="#4CAF50"
            transparent
            opacity={0.7}
          />
        </mesh>
      </group>
    );
  };

  return (
    <div className="face-3d-reconstructor">
      <div className="upload-section">
        <h3>3D Face Reconstruction</h3>
        <p>Upload a photo to generate a 3D face model</p>

        <input
          ref={fileInputRef}
          type="file"
          accept="image/*"
          onChange={handleImageUpload}
          disabled={isProcessing}
          style={{ display: 'none' }}
        />

        <button
          onClick={() => fileInputRef.current?.click()}
          disabled={isProcessing}
          className="upload-button"
        >
          {isProcessing ? 'Processing...' : 'Upload Photo for 3D Reconstruction'}
        </button>

        {progress && (
          <div className="progress-message">
            {progress}
          </div>
        )}

        {error && (
          <div className="error-message">
            Error: {error}
          </div>
        )}
      </div>

      {reconstructedFace && (
        <div className="reconstruction-result">
          <h4>3D Animated Face Model</h4>
          <div className="face-info">
            <p>Landmarks detected: {reconstructedFace.landmarks?.length || 0}</p>
            <p>Blendshapes: {reconstructedFace.blendshapes?.categories?.length || 0}</p>
            <p>Animation visemes: {reconstructedFace.availableVisemes?.length || 0}</p>
            <p className="animation-status">🎭 Lip-sync animation ready!</p>
          </div>

          <div className="face-preview" style={{ height: '400px', width: '100%' }}>
            <Canvas camera={{ position: [0, 0, 3], fov: 50 }}>
              <ambientLight intensity={0.5} />
              <directionalLight position={[10, 10, 5]} intensity={1} />
              <pointLight position={[-10, -10, -5]} intensity={0.5} />

              <ReconstructedFaceMesh faceMesh={reconstructedFace.mesh} />

              <OrbitControls
                enablePan={true}
                enableZoom={true}
                enableRotate={true}
                minDistance={1}
                maxDistance={10}
              />

              <Environment preset="studio" />
            </Canvas>
          </div>

          <div className="reconstruction-actions">
            <button
              onClick={() => {
                // Export the 3D model
                const exporter = new GLTFExporter();
                exporter.parse(
                  reconstructedFace.mesh,
                  (gltf) => {
                    const blob = new Blob([JSON.stringify(gltf)], { type: 'application/json' });
                    const url = URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = '3d-face-model.gltf';
                    a.click();
                    URL.revokeObjectURL(url);
                  },
                  { binary: false }
                );
              }}
              className="export-button"
            >
              Export 3D Model
            </button>

            <button
              onClick={() => setReconstructedFace(null)}
              className="clear-button"
            >
              Clear
            </button>
          </div>
        </div>
      )}

      <style jsx>{`
        .face-3d-reconstructor {
          padding: 20px;
          max-width: 800px;
          margin: 0 auto;
        }

        .upload-section {
          text-align: center;
          margin-bottom: 30px;
        }

        .upload-button {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          color: white;
          border: none;
          padding: 12px 24px;
          border-radius: 8px;
          font-size: 16px;
          cursor: pointer;
          transition: all 0.3s ease;
        }

        .upload-button:hover:not(:disabled) {
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
        }

        .upload-button:disabled {
          opacity: 0.6;
          cursor: not-allowed;
        }

        .progress-message {
          margin-top: 15px;
          padding: 10px;
          background: #e3f2fd;
          border-radius: 6px;
          color: #1976d2;
          font-weight: 500;
        }

        .error-message {
          margin-top: 15px;
          padding: 10px;
          background: #ffebee;
          border-radius: 6px;
          color: #c62828;
          font-weight: 500;
        }

        .reconstruction-result {
          border: 1px solid #e0e0e0;
          border-radius: 12px;
          padding: 20px;
          background: white;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .face-info {
          display: flex;
          gap: 20px;
          margin-bottom: 20px;
          font-size: 14px;
          color: #666;
          flex-wrap: wrap;
        }

        .face-info p {
          margin: 5px 0;
          padding: 5px 10px;
          background: #f8f9fa;
          border-radius: 4px;
          border-left: 3px solid #007bff;
        }

        .animation-status {
          color: #4CAF50 !important;
          font-weight: bold;
          background: #e8f5e8 !important;
          border-left-color: #4CAF50 !important;
        }

        .face-preview {
          border: 2px solid #e0e0e0;
          border-radius: 8px;
          overflow: hidden;
          margin-bottom: 20px;
          background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
          position: relative;
        }

        .face-preview::before {
          content: "🎭 Live Animation Demo";
          position: absolute;
          top: 10px;
          left: 10px;
          background: rgba(76, 175, 80, 0.9);
          color: white;
          padding: 4px 8px;
          border-radius: 4px;
          font-size: 12px;
          z-index: 10;
        }

        .reconstruction-actions {
          display: flex;
          gap: 10px;
          justify-content: center;
        }

        .export-button, .clear-button {
          padding: 8px 16px;
          border: none;
          border-radius: 6px;
          cursor: pointer;
          font-size: 14px;
          transition: all 0.3s ease;
        }

        .export-button {
          background: #4caf50;
          color: white;
        }

        .export-button:hover {
          background: #45a049;
        }

        .clear-button {
          background: #f44336;
          color: white;
        }

        .clear-button:hover {
          background: #da190b;
        }

        h3 {
          color: #333;
          margin-bottom: 10px;
        }

        h4 {
          color: #444;
          margin-bottom: 15px;
        }

        p {
          color: #666;
          margin-bottom: 20px;
        }
      `}</style>
    </div>
  );
};

export default Face3DReconstructor;
