// MediaPipe imports with fallback
let FaceLandmarker, FilesetResolver;

// Try to import MediaPipe, but don't fail if it's not available
const loadMediaPipe = async () => {
  try {
    const mediapipe = await import('@mediapipe/tasks-vision');
    FaceLandmarker = mediapipe.FaceLandmarker;
    FilesetResolver = mediapipe.FilesetResolver;
    return true;
  } catch (error) {
    console.warn('MediaPipe not available, using fallback mode:', error);
    return false;
  }
};

import * as THREE from 'three';

/**
 * 3D Face Reconstruction Service
 * Reconstructs 3D face geometry from a single 2D image using MediaPipe Face Landmarker
 * and 3D Morphable Model techniques
 */
class Face3DReconstructor {
  constructor() {
    this.faceLandmarker = null;
    this.isInitialized = false;
    this.faceTemplate = null;
    this.initializationPromise = null;
  }

  async initialize() {
    if (this.initializationPromise) {
      return this.initializationPromise;
    }

    this.initializationPromise = this._doInitialize();
    return this.initializationPromise;
  }

  async _doInitialize() {
    try {
      console.log('Initializing 3D Face Reconstructor...');

      // Try to load MediaPipe first
      const mediaPipeLoaded = await loadMediaPipe();

      if (mediaPipeLoaded) {
        try {
          console.log('Initializing MediaPipe Face Landmarker...');
          const filesetResolver = await FilesetResolver.forVisionTasks(
            "https://cdn.jsdelivr.net/npm/@mediapipe/tasks-vision@0.10.0/wasm"
          );

          this.faceLandmarker = await FaceLandmarker.createFromOptions(filesetResolver, {
            baseOptions: {
              modelAssetPath: `https://storage.googleapis.com/mediapipe-models/face_landmarker/face_landmarker/float16/1/face_landmarker.task`,
              delegate: "GPU"
            },
            outputFaceBlendshapes: true,
            outputFacialTransformationMatrixes: true,
            runningMode: "IMAGE",
            numFaces: 1
          });

          console.log('MediaPipe Face Landmarker initialized successfully');
        } catch (error) {
          console.warn('Failed to initialize MediaPipe, falling back to simplified mode:', error);
          this.faceLandmarker = null;
        }
      } else {
        console.log('Using simplified face detection (MediaPipe not available)');
      }

      // Create base face template
      this.createFaceTemplate();

      this.isInitialized = true;
      console.log('3D Face Reconstructor initialized successfully');
    } catch (error) {
      console.error('Failed to initialize 3D Face Reconstructor:', error);
      throw error;
    }
  }

  /**
   * Create a base 3D face template mesh with morphTargets for animation
   */
  createFaceTemplate() {
    // Create a more detailed face geometry template
    const geometry = new THREE.BufferGeometry();

    // Define face vertices with better topology
    const vertices = this.generateBaseFaceVertices();
    const faces = this.generateBaseFaceIndices();
    const uvs = this.generateBaseFaceUVs();

    geometry.setIndex(faces);
    geometry.setAttribute('position', new THREE.Float32BufferAttribute(vertices, 3));
    geometry.setAttribute('uv', new THREE.Float32BufferAttribute(uvs, 2));

    // Create morphTargets for facial animation (visemes)
    this.createMorphTargets(geometry);

    geometry.computeVertexNormals();
    this.faceTemplate = geometry;
  }

  /**
   * Create morphTargets for facial animation
   */
  createMorphTargets(geometry) {
    const basePositions = geometry.attributes.position.array.slice();
    const morphTargets = [];

    // Create viseme morphTargets compatible with the Avatar component
    const visemes = [
      'viseme_PP', 'viseme_kk', 'viseme_I', 'viseme_AA',
      'viseme_O', 'viseme_U', 'viseme_FF', 'viseme_TH'
    ];

    visemes.forEach(visemeName => {
      const morphPositions = basePositions.slice();
      this.applyVisemeDeformation(morphPositions, visemeName);

      morphTargets.push({
        name: visemeName,
        array: morphPositions
      });
    });

    geometry.morphAttributes.position = morphTargets;
    geometry.morphTargetsRelative = false;
  }

  /**
   * Apply viseme-specific deformations to create mouth shapes
   */
  applyVisemeDeformation(positions, visemeName) {
    const vertexCount = positions.length / 3;

    for (let i = 0; i < vertexCount; i++) {
      const x = positions[i * 3];
      const y = positions[i * 3 + 1];
      const z = positions[i * 3 + 2];

      // Apply mouth deformations based on viseme type
      if (this.isInMouthRegion(x, y)) {
        switch (visemeName) {
          case 'viseme_PP': // Closed mouth (P, B, M sounds)
            positions[i * 3 + 2] = z - 0.02; // Push lips together
            break;
          case 'viseme_AA': // Open mouth (A sound)
            if (this.isInLowerLip(x, y)) {
              positions[i * 3 + 1] = y - 0.04; // Lower jaw down
            }
            break;
          case 'viseme_O': // Round mouth (O sound)
            const distance = Math.sqrt(x * x + (y + 0.5) * (y + 0.5));
            if (distance < 0.15) {
              positions[i * 3] = x * 0.7; // Narrow horizontally
              positions[i * 3 + 2] = z + 0.01; // Push forward
            }
            break;
          case 'viseme_U': // Narrow mouth (U sound)
            if (Math.abs(x) < 0.1 && y > -0.7 && y < -0.3) {
              positions[i * 3] = x * 0.5; // Very narrow
              positions[i * 3 + 2] = z + 0.015; // Push forward more
            }
            break;
          case 'viseme_FF': // Teeth on lip (F, V sounds)
            if (this.isInUpperLip(x, y)) {
              positions[i * 3 + 1] = y - 0.01; // Slight lip compression
            }
            break;
          case 'viseme_TH': // Tongue between teeth (TH sound)
            if (this.isInLowerLip(x, y)) {
              positions[i * 3 + 1] = y + 0.01; // Slight tongue protrusion
            }
            break;
          case 'viseme_kk': // Back consonants (K, G sounds)
            if (this.isInMouthCenter(x, y)) {
              positions[i * 3 + 1] = y - 0.02; // Slight opening
            }
            break;
          case 'viseme_I': // High front vowel (I sound)
            if (this.isInMouthCorner(x, y)) {
              positions[i * 3] = x * 1.1; // Slight smile
              positions[i * 3 + 1] = y + 0.005;
            }
            break;
        }
      }
    }
  }

  // Helper methods for mouth region detection
  isInMouthRegion(x, y) {
    return Math.abs(x) < 0.3 && y > -0.8 && y < -0.2;
  }

  isInUpperLip(x, y) {
    return Math.abs(x) < 0.2 && y > -0.5 && y < -0.35;
  }

  isInLowerLip(x, y) {
    return Math.abs(x) < 0.2 && y > -0.65 && y < -0.5;
  }

  isInMouthCenter(x, y) {
    return Math.abs(x) < 0.1 && y > -0.6 && y < -0.4;
  }

  isInMouthCorner(x, y) {
    return Math.abs(x) > 0.15 && Math.abs(x) < 0.25 && y > -0.6 && y < -0.4;
  }

  /**
   * Generate base face vertices for template mesh
   */
  generateBaseFaceVertices() {
    const vertices = [];

    // Create a more realistic face mesh using a grid-based approach
    const width = 20;
    const height = 25;

    for (let y = 0; y < height; y++) {
      for (let x = 0; x < width; x++) {
        // Normalize coordinates to -1 to 1 range
        const nx = (x / (width - 1)) * 2 - 1;
        const ny = (y / (height - 1)) * 2 - 1;

        // Create face-like shape using elliptical mask
        const faceRadius = this.getFaceRadius(nx, ny);
        let z = 0;

        // Add depth variation for face features
        if (faceRadius < 1) {
          // Nose area
          if (Math.abs(nx) < 0.15 && ny > -0.3 && ny < 0.3) {
            z = 0.1 * (1 - Math.abs(ny) / 0.3);
          }
          // Eye sockets (slight depression)
          else if (this.isInEyeArea(nx, ny)) {
            z = -0.05;
          }
          // Mouth area (slight depression)
          else if (Math.abs(nx) < 0.3 && ny > -0.7 && ny < -0.3) {
            z = -0.02;
          }
          // General face curvature
          else {
            z = 0.05 * (1 - faceRadius);
          }
        }

        vertices.push(nx * 0.8, ny * 0.9, z);
      }
    }

    return vertices;
  }

  /**
   * Calculate face radius for elliptical face shape
   */
  getFaceRadius(x, y) {
    // Create an elliptical face shape
    const a = 0.8; // horizontal radius
    const b = 1.0; // vertical radius
    return Math.sqrt((x * x) / (a * a) + (y * y) / (b * b));
  }

  /**
   * Check if point is in eye area
   */
  isInEyeArea(x, y) {
    // Left eye
    const leftEyeX = -0.3;
    const leftEyeY = 0.2;
    const leftEyeDist = Math.sqrt((x - leftEyeX) ** 2 + (y - leftEyeY) ** 2);

    // Right eye
    const rightEyeX = 0.3;
    const rightEyeY = 0.2;
    const rightEyeDist = Math.sqrt((x - rightEyeX) ** 2 + (y - rightEyeY) ** 2);

    return leftEyeDist < 0.15 || rightEyeDist < 0.15;
  }

  /**
   * Generate face indices for triangulation
   */
  generateBaseFaceIndices() {
    const indices = [];
    const width = 20;
    const height = 25;

    // Create triangular mesh from grid
    for (let y = 0; y < height - 1; y++) {
      for (let x = 0; x < width - 1; x++) {
        const topLeft = y * width + x;
        const topRight = y * width + (x + 1);
        const bottomLeft = (y + 1) * width + x;
        const bottomRight = (y + 1) * width + (x + 1);

        // Check if vertices are within face boundary
        const tlx = (x / (width - 1)) * 2 - 1;
        const tly = (y / (height - 1)) * 2 - 1;
        const brx = ((x + 1) / (width - 1)) * 2 - 1;
        const bry = ((y + 1) / (height - 1)) * 2 - 1;

        // Only create triangles within the face ellipse
        if (this.getFaceRadius(tlx, tly) < 1.1 &&
            this.getFaceRadius(brx, bry) < 1.1) {

          // First triangle (top-left, bottom-left, top-right)
          indices.push(topLeft, bottomLeft, topRight);

          // Second triangle (top-right, bottom-left, bottom-right)
          indices.push(topRight, bottomLeft, bottomRight);
        }
      }
    }

    return indices;
  }

  /**
   * Generate UV coordinates for texture mapping
   */
  generateBaseFaceUVs() {
    const uvs = [];
    const width = 20;
    const height = 25;

    // Generate UV coordinates for grid-based mesh
    for (let y = 0; y < height; y++) {
      for (let x = 0; x < width; x++) {
        // Map grid coordinates to UV space (0-1)
        const u = x / (width - 1);
        const v = 1.0 - (y / (height - 1)); // Flip V coordinate

        uvs.push(u, v);
      }
    }

    return uvs;
  }

  /**
   * Reconstruct 3D face from image
   */
  async reconstruct3DFace(imageElement) {
    if (!this.isInitialized) {
      await this.initialize();
    }

    try {
      console.log('Starting 3D face reconstruction...');

      let landmarks = null;
      let blendshapes = null;
      let transformMatrix = null;

      // Try to use MediaPipe if available
      if (this.faceLandmarker) {
        try {
          console.log('Using MediaPipe for face detection...');
          const results = this.faceLandmarker.detect(imageElement);

          if (results.faceLandmarks && results.faceLandmarks.length > 0) {
            landmarks = results.faceLandmarks[0];
            blendshapes = results.faceBlendshapes ? results.faceBlendshapes[0] : null;
            transformMatrix = results.facialTransformationMatrixes ? results.facialTransformationMatrixes[0] : null;

            console.log(`Detected ${landmarks.length} face landmarks with MediaPipe`);
            if (blendshapes) {
              console.log(`Detected ${blendshapes.categories.length} blendshapes`);
            }
          } else {
            console.warn('No face detected by MediaPipe, falling back to mock landmarks');
            landmarks = this.generateMockLandmarks();
          }
        } catch (error) {
          console.warn('MediaPipe detection failed, using mock landmarks:', error);
          landmarks = this.generateMockLandmarks();
        }
      } else {
        console.log('MediaPipe not available, using mock landmarks');
        landmarks = this.generateMockLandmarks();
      }

      // Create 3D mesh from landmarks
      const mesh3D = this.createMeshFromLandmarks(landmarks, blendshapes, transformMatrix);

      // Generate enhanced texture from original image
      const texture = await this.generateEnhancedFaceTexture(imageElement, landmarks);

      return {
        mesh: mesh3D,
        texture: texture,
        landmarks: landmarks,
        blendshapes: blendshapes,
        transformMatrix: transformMatrix
      };

    } catch (error) {
      console.error('3D face reconstruction failed:', error);
      throw error;
    }
  }

  /**
   * Generate mock landmarks for demonstration
   */
  generateMockLandmarks() {
    const landmarks = [];

    // Generate realistic facial landmarks based on standard face proportions
    const faceWidth = 0.4;
    const faceHeight = 0.5;
    const centerX = 0.5;
    const centerY = 0.5;

    // Jaw line (17 points)
    for (let i = 0; i < 17; i++) {
      const angle = Math.PI + (i / 16) * Math.PI;
      const radius = faceWidth * (0.8 + 0.2 * Math.sin(angle * 2));
      landmarks.push({
        x: centerX + Math.cos(angle) * radius,
        y: centerY + Math.sin(angle) * faceHeight * 0.8,
        z: Math.random() * 0.02 - 0.01
      });
    }

    // Right eyebrow (5 points)
    for (let i = 0; i < 5; i++) {
      landmarks.push({
        x: centerX - 0.25 + (i / 4) * 0.2,
        y: centerY + 0.15,
        z: 0.01
      });
    }

    // Left eyebrow (5 points)
    for (let i = 0; i < 5; i++) {
      landmarks.push({
        x: centerX + 0.05 + (i / 4) * 0.2,
        y: centerY + 0.15,
        z: 0.01
      });
    }

    // Nose (9 points)
    const nosePoints = [
      [0, 0.05], [-0.05, 0], [0, -0.05], [0.05, 0],
      [-0.08, -0.1], [-0.04, -0.12], [0, -0.15], [0.04, -0.12], [0.08, -0.1]
    ];
    for (let point of nosePoints) {
      landmarks.push({
        x: centerX + point[0],
        y: centerY + point[1],
        z: 0.05 + Math.random() * 0.02
      });
    }

    // Right eye (6 points)
    const rightEyeCenter = [centerX - 0.15, centerY + 0.05];
    for (let i = 0; i < 6; i++) {
      const angle = (i / 6) * Math.PI * 2;
      landmarks.push({
        x: rightEyeCenter[0] + Math.cos(angle) * 0.06,
        y: rightEyeCenter[1] + Math.sin(angle) * 0.03,
        z: -0.01
      });
    }

    // Left eye (6 points)
    const leftEyeCenter = [centerX + 0.15, centerY + 0.05];
    for (let i = 0; i < 6; i++) {
      const angle = (i / 6) * Math.PI * 2;
      landmarks.push({
        x: leftEyeCenter[0] + Math.cos(angle) * 0.06,
        y: leftEyeCenter[1] + Math.sin(angle) * 0.03,
        z: -0.01
      });
    }

    // Mouth (20 points)
    const mouthCenter = [centerX, centerY - 0.15];
    for (let i = 0; i < 20; i++) {
      const angle = (i / 20) * Math.PI * 2;
      const radius = i < 12 ? 0.08 : 0.06; // Outer vs inner lip
      landmarks.push({
        x: mouthCenter[0] + Math.cos(angle) * radius,
        y: mouthCenter[1] + Math.sin(angle) * 0.04,
        z: i < 12 ? 0 : -0.01
      });
    }

    return landmarks;
  }

  /**
   * Create 3D mesh from landmarks
   */
  createMeshFromLandmarks(landmarks, blendshapes, transformMatrix) {
    const geometry = this.faceTemplate.clone();

    // For now, we'll use the template geometry as-is
    // In a full implementation, we would deform the mesh based on landmarks

    // Apply blendshapes if available
    if (blendshapes) {
      const positions = geometry.attributes.position.array;
      this.applyBlendshapes(positions, blendshapes);
      geometry.attributes.position.needsUpdate = true;
    }

    geometry.computeVertexNormals();
    return geometry;
  }

  /**
   * Apply facial expression blendshapes to mesh
   */
  applyBlendshapes(positions, blendshapes) {
    // Apply expression modifications based on blendshapes
    for (let blendshape of blendshapes.categories) {
      const weight = blendshape.score;

      // Apply specific deformations based on blendshape type
      switch (blendshape.categoryName) {
        case 'eyeBlinkLeft':
          this.applyEyeBlinkDeformation(positions, 'left', weight);
          break;
        case 'eyeBlinkRight':
          this.applyEyeBlinkDeformation(positions, 'right', weight);
          break;
        case 'mouthSmileLeft':
        case 'mouthSmileRight':
          this.applySmileDeformation(positions, weight);
          break;
        // Add more blendshape applications as needed
      }
    }
  }

  /**
   * Apply eye blink deformation
   */
  applyEyeBlinkDeformation(positions, eye, weight) {
    // Simplified eye blink - modify eye region vertices
    const eyeIndices = eye === 'left' ? [39, 40, 41, 42, 43, 44] : [45, 46, 47, 48, 49, 50];

    for (let index of eyeIndices) {
      if (index * 3 + 1 < positions.length) {
        positions[index * 3 + 1] -= weight * 0.02; // Close eye by moving vertices down
      }
    }
  }

  /**
   * Apply smile deformation
   */
  applySmileDeformation(positions, weight) {
    // Modify mouth corner vertices for smile
    const mouthCorners = [51, 55]; // Left and right mouth corners

    for (let index of mouthCorners) {
      if (index * 3 + 1 < positions.length) {
        positions[index * 3 + 1] += weight * 0.03; // Lift mouth corners
      }
    }
  }

  /**
   * Generate face texture from image
   */
  async generateFaceTexture(imageElement, landmarks) {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');

    canvas.width = 512;
    canvas.height = 512;

    // Draw the face region
    ctx.drawImage(imageElement, 0, 0, canvas.width, canvas.height);

    // Create texture
    const texture = new THREE.CanvasTexture(canvas);
    texture.flipY = false;
    texture.wrapS = THREE.ClampToEdgeWrapping;
    texture.wrapT = THREE.ClampToEdgeWrapping;

    return texture;
  }

  /**
   * Create a complete 3D face mesh with material
   */
  create3DFaceMesh(reconstructionResult) {
    const { mesh, texture } = reconstructionResult;

    const material = new THREE.MeshStandardMaterial({
      map: texture,
      transparent: true,
      side: THREE.DoubleSide
    });

    const faceMesh = new THREE.Mesh(mesh, material);
    faceMesh.castShadow = true;
    faceMesh.receiveShadow = true;

    return faceMesh;
  }
}

// Export singleton instance
export const face3DReconstructor = new Face3DReconstructor();
